package com.github.cret.web.oee.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.domain.capacity.PlannedCapacityExcelDto;
import com.github.cret.web.oee.excel.PlannedCapacityImportListener;
import com.github.cret.web.oee.mapper.PlannedCapacityMapper;
import com.github.cret.web.oee.repository.PlannedCapacityRepository;
import com.github.cret.web.oee.service.PlannedCapacityService;

import jakarta.servlet.http.HttpServletResponse;

@Service
public class PlannedCapacityServiceImpl implements PlannedCapacityService {

	private final PlannedCapacityRepository plannedCapacityRepository;

	private final PlannedCapacityMapper plannedCapacityMapper;

	public PlannedCapacityServiceImpl(PlannedCapacityRepository plannedCapacityRepository,
			PlannedCapacityMapper plannedCapacityMapper) {
		this.plannedCapacityRepository = plannedCapacityRepository;
		this.plannedCapacityMapper = plannedCapacityMapper;
	}

	@Override
	public List<PlannedCapacity> findAll() {
		return plannedCapacityRepository.findAll();
	}

	@Override
	public Optional<PlannedCapacity> findById(String id) {
		return plannedCapacityRepository.findById(id);
	}

	@Override
	public PlannedCapacity save(PlannedCapacity plannedCapacity) {
		return plannedCapacityRepository.save(plannedCapacity);
	}

	@Override
	public PlannedCapacity update(String id, PlannedCapacity plannedCapacity) {
		plannedCapacity.setId(id);
		return plannedCapacityRepository.save(plannedCapacity);
	}

	@Override
	public void deleteById(String id) {
		plannedCapacityRepository.deleteById(id);
	}

	@Override
	public void batchDelete(List<String> ids) {
		plannedCapacityRepository.deleteAllById(ids);
	}

	@Override
	public void downloadTemplate(HttpServletResponse response) throws IOException {
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());
		String fileName = URLEncoder.encode("planned_capacity_template.xlsx", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename*=" + fileName);
		PlannedCapacityExcelDto exampleData = new PlannedCapacityExcelDto();
		exampleData.setLineId("SMT1-1");
		exampleData.setWorkDateStr("2025-01-01"); // 使用 workDateStr
		exampleData.setHourOfDay(8); // 添加小时字段
		exampleData.setPlannedQuantity(1000); // 使用 plannedQuantity

		EasyExcel.write(response.getOutputStream(), PlannedCapacityExcelDto.class)
			.sheet("模板")
			.doWrite(Collections.singletonList(exampleData));
	}

	@Override

	public Map<String, Object> importFromExcel(MultipartFile file) throws IOException {
		PlannedCapacityImportListener listener = new PlannedCapacityImportListener(plannedCapacityRepository,
				plannedCapacityMapper);
		EasyExcel.read(file.getInputStream(), PlannedCapacityExcelDto.class, listener).sheet().doRead();

		Map<String, Object> result = new HashMap<>();
		result.put("totalRows", listener.getSuccessList().size() + listener.getFailureList().size());
		result.put("successRows", listener.getSuccessList().size());
		result.put("failureRows", listener.getFailureList().size());
		result.put("failures", listener.getFailureList());

		return result;
	}

	@Override
	public PageList<PlannedCapacity> page(PageableParam<PlannedCapacity> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("lineId", GenericPropertyMatchers.exact())
			.withMatcher("workDate", GenericPropertyMatchers.exact());
		Example<PlannedCapacity> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(plannedCapacityRepository.findAll(example, param.getPageRequest()));
	}

	@Override
	public void exportToExcel(HttpServletResponse response, PlannedCapacity searchParams) throws IOException {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("lineId", ExampleMatcher.GenericPropertyMatchers.exact())
			.withMatcher("workDate", ExampleMatcher.GenericPropertyMatchers.exact());
		Example<PlannedCapacity> example = Example.of(searchParams, matcher);
		List<PlannedCapacity> plannedCapacities = plannedCapacityRepository.findAll(example);

		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());
		String fileName = URLEncoder.encode("planned_capacities.xlsx", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename*=" + fileName);

		EasyExcel.write(response.getOutputStream(), PlannedCapacityExcelDto.class)
			.sheet("计划产能")
			.doWrite(plannedCapacityMapper.toExcelDto(plannedCapacities));
	}

}
