package com.github.cret.web.oee.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.UnmetReason;
import com.github.cret.web.oee.service.UnmetReasonService;

@RestController
@RequestMapping("/unmet-reasons")
public class UnmetReasonController {

	private final UnmetReasonService unmetReasonService;

	public UnmetReasonController(UnmetReasonService unmetReasonService) {
		this.unmetReasonService = unmetReasonService;
	}

	@PostMapping("/page")
	public PageList<UnmetReason> page(@RequestBody PageableParam<UnmetReason> param) {
		return unmetReasonService.page(param);
	}

	@GetMapping
	public List<UnmetReason> findAll() {
		return unmetReasonService.findAll();
	}

	@GetMapping("/{id}")
	public Optional<UnmetReason> findById(@PathVariable String id) {
		return unmetReasonService.findById(id);
	}

	@PostMapping
	public UnmetReason save(@RequestBody UnmetReason unmetReason) {
		return unmetReasonService.save(unmetReason);
	}

	@PutMapping("/{id}")
	public UnmetReason update(@PathVariable String id, @RequestBody UnmetReason unmetReason) {
		return unmetReasonService.update(id, unmetReason);
	}

	@DeleteMapping("/{id}")
	public void deleteById(@PathVariable String id) {
		unmetReasonService.deleteById(id);
	}

	@DeleteMapping("/batch")
	public void batchDelete(@RequestBody List<String> ids) {
		unmetReasonService.batchDelete(ids);
	}

}
