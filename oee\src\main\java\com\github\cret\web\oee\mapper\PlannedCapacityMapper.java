package com.github.cret.web.oee.mapper;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.domain.capacity.PlannedCapacityExcelDto;

@Mapper(componentModel = "spring")
public interface PlannedCapacityMapper {

	/**
	 * 将DTO映射到实体。 使用自定义的 normalizeDateString 方法来处理日期字符串的转换和格式化。
	 */
	@Mapping(source = "workDateStr", target = "workDate", qualifiedByName = "normalizeDateString")
	@Mapping(target = "id", ignore = true)
	PlannedCapacity toEntity(PlannedCapacityExcelDto dto);

	/**
	 * 注意：如果 PlannedCapacity 实体中的 workDate 字段确实是 String 类型， 那么这里的 dateFormat
	 * 属性将无法正常工作，因为它期望源属性是日期/时间类型（如LocalDate）。 如果 workDate 是 "yyyy-MM-dd" 格式的字符串，直接映射即可，无需
	 * dateFormat。 例如：@Mapping(source = "workDate", target = "workDateStr")
	 */
	@Mapping(source = "workDate", target = "workDateStr", dateFormat = "yyyy-MM-dd")
	PlannedCapacityExcelDto toExcelDto(PlannedCapacity entity);

	List<PlannedCapacityExcelDto> toExcelDto(List<PlannedCapacity> entity);

	/**
	 * 新增的方法：将传入的多种格式的日期字符串统一格式化为 "yyyy-MM-dd" 标准格式的字符串。 使用 @Named 注解，以便 @Mapping 可以通过
	 * qualifiedByName 来调用它。
	 * @param dateStr 待处理的日期字符串
	 * @return 格式化为 "yyyy-MM-dd" 的字符串
	 */
	@Named("normalizeDateString")
	default String normalizeDateString(String dateStr) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			return null;
		}

		LocalDate localDate = stringToLocalDate(dateStr); // 复用现有的解析逻辑
		return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
	}

	default LocalDate stringToLocalDate(String dateStr) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			return null;
		}

		// 这个格式化程序将首先尝试匹配 yyyy-MM-dd，然后是其他格式
		DateTimeFormatter multiFormatter = new DateTimeFormatterBuilder()
			.appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
			.appendOptional(DateTimeFormatter.ofPattern("yyyy-M-d"))
			.appendOptional(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
			.appendOptional(DateTimeFormatter.ofPattern("yyyy/M/d"))
			.toFormatter();

		try {
			return LocalDate.parse(dateStr, multiFormatter);
		}
		catch (DateTimeParseException e) {
			throw new IllegalArgumentException("无效的日期格式 '" + dateStr + "'. 请使用 'yyyy-MM-dd' 或 'yyyy/MM/dd' 格式。", e);
		}
	}

}