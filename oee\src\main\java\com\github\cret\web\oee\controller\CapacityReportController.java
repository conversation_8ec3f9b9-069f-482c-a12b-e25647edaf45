package com.github.cret.web.oee.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.oee.domain.capacity.CapacityReportWithLine;
import com.github.cret.web.oee.service.CapacityReportService;

@RestController
@RequestMapping("/capacity")
public class CapacityReportController {

	private final CapacityReportService capacityReportService;

	public CapacityReportController(CapacityReportService capacityReportService) {
		this.capacityReportService = capacityReportService;
	}

	/**
	 * 获取产能报表数据
	 * @param lineId 产线ID
	 * @param workDate 工作日期 (格式: YYYY-MM-DD)
	 * @return 小时维度的产能数据列表
	 */
	@GetMapping("/report")
	public List<CapacityReportWithLine> getCapacityReport(@RequestParam String workDate) {
		return capacityReportService.getCapacityReport(workDate);
	}

}
