package com.github.cret.web.oee.repository;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;

import com.github.cret.web.oee.document.PlannedCapacity;

public interface PlannedCapacityRepository extends MongoRepository<PlannedCapacity, String> {

	List<PlannedCapacity> findByLineIdAndWorkDate(String lineId, LocalDate workDate);

}