package com.github.cret.web.oee.domain.capacity;

import com.alibaba.excel.annotation.ExcelProperty;

public class PlannedCapacityExcelDto {

	@ExcelProperty("产线编码")
	private String lineId;

	@ExcelProperty("工作日期")
	private String workDateStr; // 先用字符串接收，便于自定义转换和校验

	@ExcelProperty("小时")
	private Integer hourOfDay;

	@ExcelProperty("计划产能")
	private Integer plannedQuantity;

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getWorkDateStr() {
		return workDateStr;
	}

	public void setWorkDateStr(String workDateStr) {
		this.workDateStr = workDateStr;
	}

	public Integer getHourOfDay() {
		return hourOfDay;
	}

	public void setHourOfDay(Integer hourOfDay) {
		this.hourOfDay = hourOfDay;
	}

	public Integer getPlannedQuantity() {
		return plannedQuantity;
	}

	public void setPlannedQuantity(Integer plannedQuantity) {
		this.plannedQuantity = plannedQuantity;
	}

}
