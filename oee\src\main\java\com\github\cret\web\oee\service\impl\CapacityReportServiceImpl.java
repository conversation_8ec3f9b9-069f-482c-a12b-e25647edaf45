package com.github.cret.web.oee.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.calculator.HourlyOutputCalculator;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.ProductionLine;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.HourlyOutput;
import com.github.cret.web.oee.domain.capacity.CapacityReportDto;
import com.github.cret.web.oee.domain.capacity.CapacityReportWithLine;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.repository.PlannedCapacityRepository;
import com.github.cret.web.oee.service.CapacityReportService;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.ProductionLineService;
import com.github.cret.web.oee.utils.BuilderUtil;

@Service
public class CapacityReportServiceImpl implements CapacityReportService {

	private final ProductionLineService productionLineService;

	private final DeviceService deviceService;

	private final PlannedCapacityRepository plannedCapacityRepository;

	private final MongoTemplate mongoTemplate;

	public CapacityReportServiceImpl(ProductionLineService productionLineService,
			PlannedCapacityRepository plannedCapacityRepository, MongoTemplate mongoTemplate,
			DeviceService deviceService) {
		this.productionLineService = productionLineService;
		this.plannedCapacityRepository = plannedCapacityRepository;
		this.mongoTemplate = mongoTemplate;
		this.deviceService = deviceService;
	}

	@Override
	public List<CapacityReportWithLine> getCapacityReport(String workDate) {
		List<CapacityReportWithLine> capacityReportWithLines = new ArrayList<>();

		// 1. 获取所有启用的线体
		List<ProductionLine> productionLines = productionLineService.getList()
			.stream()
			.filter(line -> line.getEnable() != null && line.getEnable() == 1)
			.toList();

		// 2. 遍历每条线体，获取产能报表数据
		for (ProductionLine productionLine : productionLines) {
			CapacityReportWithLine capacityReportWithLine = BuilderUtil.builder(CapacityReportWithLine::new)
				.with(CapacityReportWithLine::setLineCode, productionLine.getCode())
				.with(CapacityReportWithLine::setCapacityReportDtos,
						getLineCapacityReport(productionLine.getCode(), workDate))
				.build();
			capacityReportWithLines.add(capacityReportWithLine);
		}

		return capacityReportWithLines;
	}

	/**
	 * 获取单条线体的产能报表数据
	 * @param lineId
	 * @param workDate
	 * @return
	 */
	private List<CapacityReportDto> getLineCapacityReport(String lineId, String workDate) {

		List<CapacityReportDto> report = new ArrayList<>();
		LocalDateTime startDateTime = LocalDate.parse(workDate).atTime(8, 0);
		LocalDateTime endDateTime = startDateTime.plusDays(1);

		// 获取实际产能数据
		Map<String, Integer> actualCapacities = getActualCapacities(lineId, workDate);

		// 获取计划产能数据
		Map<String, Integer> plannedCapacities = getPlannedCapacities(lineId, startDateTime, endDateTime);

		Map<String, Integer> unmetReasons = getUumetReasons(lineId, startDateTime, endDateTime);

		for (LocalDateTime currentHourStart = startDateTime; currentHourStart
			.isBefore(endDateTime); currentHourStart = currentHourStart.plusHours(1)) {

			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			String date = currentHourStart.format(formatter);
			int hour = currentHourStart.getHour();

			int plannedQuantity = plannedCapacities.getOrDefault(hour, 0);
			int actualQuantity = actualCapacities.getOrDefault(hour, 0);
			double achievementRate = (double) actualQuantity / plannedQuantity;
			String status = achievementRate >= 1.0 ? "达成" : "未达成";
			String reason = achievementRate >= 1.0 ? "" : "原因待查";

			CapacityReportDto capacityReportDto = BuilderUtil.builder(CapacityReportDto::new)
				.with(CapacityReportDto::setWorkDate, date)
				.with(CapacityReportDto::setHour, hour)
				.with(CapacityReportDto::setPlannedQuantity, plannedQuantity)
				.with(CapacityReportDto::setActualQuantity, actualQuantity)
				.with(CapacityReportDto::setAchievementRate, String.format("%.2f", achievementRate))
				.with(CapacityReportDto::setStatus, status)
				.with(CapacityReportDto::setReason, reason)
				.build();
			report.add(capacityReportDto);

		}
		return report;
	}

	/**
	 * @param lineId
	 * @param startDateTime
	 * @param endTime
	 * @return
	 */
	private Map<String, Integer> getPlannedCapacities(String lineId, LocalDateTime startDateTime,
			LocalDateTime endTime) {
		return null;
	}

	/**
	 * 获取实际产能数据
	 * @param lineId
	 * @param workDate
	 * @return
	 */
	private Map<String, Integer> getActualCapacities(String lineId, String workDate) {
		Device capacityDevice = getCapacityDevice(lineId);

		AnalyzeQuery query = BuilderUtil.builder(AnalyzeQuery::new)
			.with(AnalyzeQuery::generateTimeRangeByWorkDate, workDate)
			.build();

		List<HourlyOutput> hourlyActualOutput = HourlyOutputCalculator.getHourlyActualOutput(capacityDevice, query,
				mongoTemplate);

		if (hourlyActualOutput == null || hourlyActualOutput.isEmpty()) {
			return Collections.emptyMap();
		}

		Map<String, Integer> actualCapacities = hourlyActualOutput.stream()
			.collect(Collectors.toMap(HourlyOutput::getTime, HourlyOutput::getCount, Integer::sum));

		return actualCapacities;
	}

	private Map<String, Integer> getUumetReasons(String lineId, LocalDateTime startDateTime, LocalDateTime endTime) {
		return null;
	}

	/**
	 * 获取计算实际产能的设备
	 * @param lineId
	 * @return
	 */
	private Device getCapacityDevice(String lineId) {
		// 返回贴片机的最后一台设备
		List<Device> devices = deviceService.getDevicesByCategory(lineId, DeviceCategory.SMT);
		return devices.get(devices.size() - 1);

	}

}
