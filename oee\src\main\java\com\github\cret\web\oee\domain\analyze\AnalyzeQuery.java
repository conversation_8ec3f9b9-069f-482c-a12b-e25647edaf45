package com.github.cret.web.oee.domain.analyze;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.oee.enums.LineType;

import cn.hutool.core.date.DateUtil;

/**
 * 分析查询条件
 */
public class AnalyzeQuery {

	// 线体编码
	String code;

	// 线体类型
	LineType lineType;

	// 产品型号
	String productModel;

	// 开始时间
	Date startTime;

	// 结束时间
	Date endTime;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public LineType getLineType() {
		return lineType;
	}

	public void setLineType(LineType lineType) {
		this.lineType = lineType;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public void validateTimeRange() {
		Date now = new Date();
		// 仅当两个时间均为null时重置（根据需求调整）
		if (this.startTime == null || this.endTime == null) {
			String today = DateUtil.formatDate(now);
			this.startTime = DateUtil.parseDateTime(today + " 08:00:00");
			this.endTime = DateUtil.parseDateTime(today + " 20:00:00");
		}
		// 可选：增加时间范围合法性校验
		if (this.startTime.after(this.endTime)) {
			throw SysErrEnum.INVALID_PARAMETER.exception("开始时间不能晚于结束时间");
		}
	}

	/**
	 * 生成时间范围
	 */
	public void generateTimeRange() {
		// 使用服务器系统默认时区
		ZoneId zoneId = ZoneId.systemDefault();

		// 1. 获取当前时区的当前时间
		ZonedDateTime now = ZonedDateTime.now(zoneId);

		// 2. 将时间部分设置为当天的8点整，得到 ZonedDateTime 类型的 startTime
		ZonedDateTime startTimeZdt = now.with(LocalTime.of(8, 0, 0, 0));

		// 3. 在 startTime 的基础上加一天，得到 endTime
		ZonedDateTime endTimeZdt = startTimeZdt.plusDays(1);

		// 4. 将 ZonedDateTime 转换回 java.util.Date 类型
		this.startTime = Date.from(startTimeZdt.toInstant());
		this.endTime = Date.from(endTimeZdt.toInstant());
	}

	public void generateHistoricalTimeRange(Integer startDay) {
		// 使用服务器系统默认时区
		ZoneId zoneId = ZoneId.systemDefault();

		// 1. 获取当前时区的当前时间
		ZonedDateTime now = ZonedDateTime.now(zoneId);

		// 2. 将时间部分设置为当天的8点整，得到 ZonedDateTime 类型的 startTime
		ZonedDateTime startTimeZdt = now.with(LocalTime.of(8, 0, 0, 0));

		// 3. 在 startTime 的基础上减去3天，得到 startTime
		startTimeZdt = startTimeZdt.minusDays(startDay);

		// 4. 在 startTime 的基础上加一天，得到 endTime
		ZonedDateTime endTimeZdt = startTimeZdt.plusDays(1);

		// 5. 将 ZonedDateTime 转换回 java.util.Date 类型
		this.startTime = Date.from(startTimeZdt.toInstant());
		this.endTime = Date.from(endTimeZdt.toInstant());
	}

	/**
	 * 根据工作日期生成时间范围
	 * @param workDate
	 */
	public void generateTimeRangeByWorkDate(String workDate) {
		if (workDate == null || workDate.trim().isEmpty()) {
			throw SysErrEnum.INVALID_PARAMETER.exception("工作日期不能为空");
		}

		try {
			// 定义日期格式化器
			DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			// 解析工作日期字符串
			LocalDate parsedDate = LocalDate.parse(workDate, dateFormatter);

			// 定义开始时间点（8点整）
			LocalTime startTimeOfDay = LocalTime.of(8, 0, 0);

			// 组合日期和时间，生成开始时间的 LocalDateTime
			LocalDateTime startDateTime = parsedDate.atTime(startTimeOfDay);

			// 结束时间为开始时间加一天
			LocalDateTime endDateTime = startDateTime.plusDays(1);

			// 使用系统默认时区，与已有方法保持一致
			ZoneId zoneId = ZoneId.systemDefault();

			// 将 LocalDateTime 转换为 Date 类型并设置到成员变量
			this.startTime = Date.from(startDateTime.atZone(zoneId).toInstant());
			this.endTime = Date.from(endDateTime.atZone(zoneId).toInstant());

		}
		catch (DateTimeParseException e) {
			// 如果日期格式不正确，抛出异常
			throw SysErrEnum.INVALID_PARAMETER.exception("工作日期格式不正确，应为 yyyy-MM-dd");
		}
	}

}
