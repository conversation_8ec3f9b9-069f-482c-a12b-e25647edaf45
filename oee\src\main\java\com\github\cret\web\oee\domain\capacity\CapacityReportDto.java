package com.github.cret.web.oee.domain.capacity;

/**
 * 产能报表数据传输对象
 */
public class CapacityReportDto {

	// 工作日期
	private String workDate;

	// 小时
	private int hour;

	// 计划产能
	private int plannedQuantity;

	// 实际产能
	private int actualQuantity;

	// 达成率
	private String achievementRate;

	// 达成状态
	private String status;

	// 其他原因
	private String reason;

	public String getWorkDate() {
		return workDate;
	}

	public void setWorkDate(String workDate) {
		this.workDate = workDate;
	}

	public int getHour() {
		return hour;
	}

	public void setHour(int hour) {
		this.hour = hour;
	}

	public int getPlannedQuantity() {
		return plannedQuantity;
	}

	public void setPlannedQuantity(int plannedQuantity) {
		this.plannedQuantity = plannedQuantity;
	}

	public int getActualQuantity() {
		return actualQuantity;
	}

	public void setActualQuantity(int actualQuantity) {
		this.actualQuantity = actualQuantity;
	}

	public String getAchievementRate() {
		return achievementRate;
	}

	public void setAchievementRate(String achievementRate) {
		this.achievementRate = achievementRate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

}
